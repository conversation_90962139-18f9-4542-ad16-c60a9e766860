// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  phone?: string
  avatar?: string
  nickname?: string
  bio?: string
  role: 'USER' | 'MERCHANT' | 'ADMIN'
  status: 'ACTIVE' | 'INACTIVE' | 'BANNED'
  createdAt: Date
  updatedAt: Date
}

export interface UserProfile extends Omit<User, 'password'> {
  followersCount: number
  followingCount: number
  postsCount: number
}

// 商品相关类型
export interface Product {
  id: number
  name: string
  description: string
  price: number
  originalPrice?: number
  images: string[]
  categoryId: number
  merchantId: number
  stock: number
  sales: number
  rating: number
  reviewCount: number
  status: 'ACTIVE' | 'INACTIVE' | 'OUT_OF_STOCK'
  tags: string[]
  specifications: Record<string, any>
  createdAt: Date
  updatedAt: Date
}

// 包含关联数据的商品类型
export interface ProductWithRelations extends Product {
  category?: ProductCategory
  merchant?: Pick<User, 'id' | 'username' | 'nickname' | 'avatar' | 'role'>
  favoriteCount?: number
}

export interface ProductCategory {
  id: number
  name: string
  slug: string
  description?: string
  parentId?: number
  image?: string
  sort: number
  status: 'ACTIVE' | 'INACTIVE'
}

// 订单相关类型
export interface Order {
  id: number
  orderNo: string
  userId: number
  totalAmount: number
  discountAmount: number
  shippingAmount: number
  paymentAmount: number
  status: 'PENDING' | 'PAID' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED' | 'REFUNDED'
  paymentStatus: 'PENDING' | 'PAID' | 'FAILED' | 'REFUNDED'
  paymentMethod?: 'ALIPAY' | 'WECHAT' | 'BANK_CARD'
  shippingAddress: ShippingAddress
  items: OrderItem[]
  createdAt: Date
  updatedAt: Date
}

export interface OrderItem {
  id: number
  orderId: number
  productId: number
  productName: string
  productImage: string
  price: number
  quantity: number
  totalAmount: number
}

export interface ShippingAddress {
  id: number
  userId: number
  name: string
  phone: string
  province: string
  city: string
  district: string
  address: string
  postalCode?: string
  isDefault: boolean
}

// 购物车类型
export interface CartItem {
  id: number
  userId: number
  productId: number
  quantity: number
  selected: boolean
  createdAt: Date
  updatedAt: Date
  product: ProductWithRelations
}

// 社交相关类型
export interface Post {
  id: number
  userId: number
  content: string
  images?: string[]
  type: 'TEXT' | 'IMAGE' | 'PRODUCT_SHARE'
  productId?: number
  product?: ProductWithRelations
  likesCount: number
  commentsCount: number
  sharesCount: number
  status: 'PUBLISHED' | 'DRAFT' | 'DELETED'
  createdAt: Date
  updatedAt: Date
  user: User
  isLiked?: boolean
  comments?: Comment[]
}

export interface Comment {
  id: number
  postId: number
  userId: number
  content: string
  parentId?: number
  likesCount: number
  createdAt: Date
  updatedAt: Date
  user: User
  replies?: Comment[]
}

export interface Follow {
  id: number
  followerId: number
  followingId: number
  createdAt: Date
}

export interface Message {
  id: number
  senderId: number
  receiverId: number
  content: string
  type: 'TEXT' | 'IMAGE' | 'SYSTEM'
  isRead: boolean
  createdAt: Date
  sender: User
  receiver: User
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  errors?: any[]
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 表单类型
export interface LoginForm {
  username: string
  password: string
  captcha?: string
  rememberMe?: boolean
}

export interface RegisterForm {
  username: string
  email: string
  password: string
  confirmPassword: string
  phone?: string
  captcha: string
}

export interface ProductSearchParams {
  keyword?: string
  categoryId?: number
  minPrice?: number
  maxPrice?: number
  sortBy?: 'price' | 'sales' | 'rating' | 'created_at'
  sortOrder?: 'asc' | 'desc'
  page?: number
  pageSize?: number
}

// 支付相关类型
export interface PaymentRequest {
  orderId: number
  paymentMethod: 'ALIPAY' | 'WECHAT'
  returnUrl?: string
}

export interface PaymentResponse {
  paymentId: string
  paymentUrl?: string
  qrCode?: string
  status: 'PENDING' | 'SUCCESS' | 'FAILED'
}

export interface Comment {
  id: number
  postId: number
  userId: number
  content: string
  createdAt: Date
  updatedAt: Date
  user: User
}

export interface Follow {
  id: number
  followerId: number
  followingId: number
  createdAt: Date
  follower: User
  following: User
}

// 通知相关类型
export interface Notification {
  id: number
  userId: number
  type: 'LIKE' | 'COMMENT' | 'FOLLOW' | 'ORDER' | 'PAYMENT' | 'SYSTEM' | 'PROMOTION'
  title: string
  content: string
  actionUrl?: string
  isRead: boolean
  createdAt: Date
  updatedAt: Date
}

// 其他类型
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  errors?: string[]
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}
