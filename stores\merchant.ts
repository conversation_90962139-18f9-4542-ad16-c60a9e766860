import type { Order, PaginatedResponse, ProductWithRelations } from '~/types'

/**
 * 商家管理状态管理
 */
export const useMerchantStore = defineStore('merchant', () => {
  // 状态
  const products = ref<ProductWithRelations[]>([])
  const orders = ref<Order[]>([])
  const statistics = ref({
    totalProducts: 0,
    totalOrders: 0,
    totalRevenue: 0,
    totalCustomers: 0,
    todayOrders: 0,
    todayRevenue: 0,
    pendingOrders: 0,
    lowStockProducts: 0
  })
  const isLoading = ref(false)
  const productsPagination = ref({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0
  })
  const ordersPagination = ref({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0
  })

  // API实例
  const request = createAuthenticatedRequest()
  const authStore = useAuthStore()
  const toast = useToast()

  // 检查商家权限
  const checkMerchantPermission = () => {
    if (!authStore.isLoggedIn || !authStore.hasPermission('MERCHANT')) {
      throw new Error('需要商家权限')
    }
  }

  // 获取商家统计数据
  const fetchStatistics = async () => {
    try {
      checkMerchantPermission()

      const stats = await request.get('/api/merchant/statistics')
      statistics.value = stats
    } catch (error: any) {
      console.error('获取统计数据失败:', error)
      toast.add({
        title: '获取统计数据失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
    }
  }

  // 获取商家商品列表
  const fetchProducts = async (params: Record<string, any> = {}) => {
    try {
      checkMerchantPermission()
      isLoading.value = true

      const response = await request.get<PaginatedResponse<ProductWithRelations>>('/api/merchant/products', {
        params: {
          page: productsPagination.value.page,
          pageSize: productsPagination.value.pageSize,
          ...params
        }
      })

      products.value = response.items
      productsPagination.value = {
        page: response.page,
        pageSize: response.pageSize,
        total: response.total,
        totalPages: response.totalPages
      }
    } catch (error: any) {
      console.error('获取商品列表失败:', error)
      toast.add({
        title: '获取商品列表失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
    } finally {
      isLoading.value = false
    }
  }

  // 创建商品
  const createProduct = async (productData: Partial<ProductWithRelations>) => {
    try {
      checkMerchantPermission()

      const product = await request.post<ProductWithRelations>('/api/merchant/products', productData)

      // 添加到列表开头
      products.value.unshift(product)

      toast.add({
        title: '商品创建成功',
        color: 'green'
      })

      return product
    } catch (error: any) {
      console.error('创建商品失败:', error)
      toast.add({
        title: '创建商品失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 更新商品
  const updateProduct = async (productId: number, productData: Partial<Product>) => {
    try {
      checkMerchantPermission()

      const product = await request.put<Product>(`/api/merchant/products/${productId}`, productData)

      // 更新本地状态
      const index = products.value.findIndex(p => p.id === productId)
      if (index !== -1) {
        products.value[index] = product
      }

      toast.add({
        title: '商品更新成功',
        color: 'green'
      })

      return product
    } catch (error: any) {
      console.error('更新商品失败:', error)
      toast.add({
        title: '更新商品失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 删除商品
  const deleteProduct = async (productId: number) => {
    try {
      checkMerchantPermission()

      await request.delete(`/api/merchant/products/${productId}`)

      // 从本地列表中移除
      const index = products.value.findIndex(p => p.id === productId)
      if (index !== -1) {
        products.value.splice(index, 1)
      }

      toast.add({
        title: '商品删除成功',
        color: 'blue'
      })
    } catch (error: any) {
      console.error('删除商品失败:', error)
      toast.add({
        title: '删除商品失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 上架/下架商品
  const toggleProductStatus = async (productId: number, status: 'ACTIVE' | 'INACTIVE') => {
    try {
      checkMerchantPermission()

      const product = await request.patch<Product>(`/api/merchant/products/${productId}/status`, {
        status
      })

      // 更新本地状态
      const index = products.value.findIndex(p => p.id === productId)
      if (index !== -1) {
        products.value[index] = product
      }

      toast.add({
        title: status === 'ACTIVE' ? '商品已上架' : '商品已下架',
        color: 'green'
      })

      return product
    } catch (error: any) {
      console.error('更新商品状态失败:', error)
      toast.add({
        title: '操作失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 获取商家订单列表
  const fetchOrders = async (params: Record<string, any> = {}) => {
    try {
      checkMerchantPermission()
      isLoading.value = true

      const response = await request.get<PaginatedResponse<Order>>('/api/merchant/orders', {
        params: {
          page: ordersPagination.value.page,
          pageSize: ordersPagination.value.pageSize,
          ...params
        }
      })

      orders.value = response.items
      ordersPagination.value = {
        page: response.page,
        pageSize: response.pageSize,
        total: response.total,
        totalPages: response.totalPages
      }
    } catch (error: any) {
      console.error('获取订单列表失败:', error)
      toast.add({
        title: '获取订单列表失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
    } finally {
      isLoading.value = false
    }
  }

  // 处理订单
  const processOrder = async (orderId: number, action: 'CONFIRM' | 'SHIP' | 'CANCEL', data?: any) => {
    try {
      checkMerchantPermission()

      const order = await request.patch<Order>(`/api/merchant/orders/${orderId}/${action.toLowerCase()}`, data)

      // 更新本地状态
      const index = orders.value.findIndex(o => o.id === orderId)
      if (index !== -1) {
        orders.value[index] = order
      }

      const actionTexts = {
        CONFIRM: '确认',
        SHIP: '发货',
        CANCEL: '取消'
      }

      toast.add({
        title: `订单${actionTexts[action]}成功`,
        color: 'green'
      })

      return order
    } catch (error: any) {
      console.error('处理订单失败:', error)
      toast.add({
        title: '处理订单失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 批量操作商品
  const batchUpdateProducts = async (productIds: number[], action: string, data?: any) => {
    try {
      checkMerchantPermission()

      await request.patch('/api/merchant/products/batch', {
        productIds,
        action,
        data
      })

      // 重新获取商品列表
      await fetchProducts()

      toast.add({
        title: '批量操作成功',
        color: 'green'
      })
    } catch (error: any) {
      console.error('批量操作失败:', error)
      toast.add({
        title: '批量操作失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 导出数据
  const exportData = async (type: 'products' | 'orders', params?: Record<string, any>) => {
    try {
      checkMerchantPermission()

      const response = await request.get(`/api/merchant/export/${type}`, {
        params
      })

      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response]))
      const link = document.createElement('a')
      link.href = url
      link.download = `${type}_${new Date().toISOString().split('T')[0]}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      toast.add({
        title: '导出成功',
        color: 'green'
      })
    } catch (error: any) {
      console.error('导出数据失败:', error)
      toast.add({
        title: '导出失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 计算属性
  const isMerchant = computed(() => authStore.hasPermission('MERCHANT'))
  const lowStockProducts = computed(() => products.value.filter(product => product.stock <= 10))
  const pendingOrders = computed(() => orders.value.filter(order => order.status === 'PENDING'))

  // 清空状态
  const clearState = () => {
    products.value = []
    orders.value = []
    statistics.value = {
      totalProducts: 0,
      totalOrders: 0,
      totalRevenue: 0,
      totalCustomers: 0,
      todayOrders: 0,
      todayRevenue: 0,
      pendingOrders: 0,
      lowStockProducts: 0
    }
    productsPagination.value = {
      page: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0
    }
    ordersPagination.value = {
      page: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0
    }
  }

  return {
    // 状态
    products: readonly(products),
    orders: readonly(orders),
    statistics: readonly(statistics),
    isLoading: readonly(isLoading),
    productsPagination: readonly(productsPagination),
    ordersPagination: readonly(ordersPagination),

    // 方法
    fetchStatistics,
    fetchProducts,
    createProduct,
    updateProduct,
    deleteProduct,
    toggleProductStatus,
    fetchOrders,
    processOrder,
    batchUpdateProducts,
    exportData,

    // 计算属性
    isMerchant,
    lowStockProducts,
    pendingOrders,

    // 工具方法
    clearState
  }
})
